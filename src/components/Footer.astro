---
export const prerender = true
import { trpc } from '@/trpc'

// export const prerender = true
// Добавляем заголовки кеширования
Astro.response.headers.set('Cache-Control', 'public, max-age=86400, stale-while-revalidate=604800'); // 86400 секунд = 24 часа, stale-while-revalidate на неделю
Astro.response.headers.set('Vary', 'Accept, Cookie, Accept-Encoding, Accept-Language, User-Agent')

// Добавляем заголовок для предотвращения предзагрузки
Astro.response.headers.set('X-Astro-Preload', 'false');

const htmlBody = await trpc.services.getHtmkChunk.query({ id: 1 }, {
  context: {
    headers: {
      'Cache-Control': 'public, max-age=86400'
    }
  }
})
---
<footer class='h-full bg-zinc-900 dark:bg-zinc-800 text-white p-3 md:p-5'>
  <div set:html={htmlBody?.body} />
</footer>
