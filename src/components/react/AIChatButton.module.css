.borderBeam {
  position: relative;
  isolation: isolate;
}

.borderBeam::before,
.borderBeam::after {
  content: '';
  position: absolute;
  inset: 0;
  z-index: -1;
}

.borderBeam::before {
  background: linear-gradient(
    90deg,
    #f09433,
    #e6683c 25%,
    #dc2743 50%,
    #cc2366 75%,
    #bc1888
  );
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.borderBeam::after {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 100%
  );
  animation: beam 3s linear infinite;
  translate: -50% 0;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.borderBeam:hover::before,
.borderBeam:hover::after {
  opacity: 1;
}

@keyframes beam {
  100% {
    translate: 150% 0;
  }
}
