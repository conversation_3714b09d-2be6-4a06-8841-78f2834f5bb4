import { <PERSON>, CardB<PERSON>, Badge, Drawer, Drawer<PERSON>ontent, DrawerBody } from '@heroui/react'
import { HomeIcon, SearchIcon, ShoppingBasketIcon } from 'lucide-react'
import { useState, useMemo, useEffect } from 'react'
import SearchBar from './SearchBar'
import { useStore } from '@tanstack/react-store'
import { cartTotal } from '@/stores/cart'

export const MobileBottomBar = ({ isMobile = true, isIOS = false }) => {
  const memoizedHomeIcon = useMemo(() => <HomeIcon className='w-5' />, [])

  const HomeItem = () => {
    return (
      <a className='rounded-lg border-2 bg-default-100 p-1' href='/' aria-label='Перейти на главную страницу'>
        <div className='flex items-center gap-2'>
          {memoizedHomeIcon}
          <span className='text-default-900'>Главная</span>
        </div>
      </a>
    )
  }

  const CartItem = () => {
    const $cartTotalQty = useStore(cartTotal)

    const memoizedBadge = useMemo(
      () => (
        <Badge placement='top-left' color='warning' variant='flat' content={$cartTotalQty || 0}>
          <ShoppingBasketIcon className='w-5' />
          <span className='ml-1 text-default-900'>Корзина</span>
        </Badge>
      ),
      [$cartTotalQty]
    )

    return (
      <div className='flex items-center gap-3 rounded-lg border-2 bg-default-100 p-1 py-0'>
        <a className='mt-1' href='/cart' aria-label='items-center Перейти в корзину'>
          {memoizedBadge}
        </a>
      </div>
    )
  }

  const SearchItem = () => {
    const [isOpen, setIsOpen] = useState(false)

    useEffect(() => {
      if (!isOpen) return

      const handlePopState = () => setIsOpen(false)
      window.history.pushState(null, '', window.location.href)
      window.addEventListener('popstate', handlePopState)

      // Исправление для iOS - предотвращение скрытия drawer при появлении клавиатуры
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as any).MSStream
      if (isIOS) {
        document.body.style.position = 'fixed'
        document.body.style.width = '100%'
        document.body.style.height = '100%'
        document.body.style.overflow = 'hidden'
      }

      return () => {
        window.removeEventListener('popstate', handlePopState)

        // Восстановление стилей при закрытии drawer
        if (isIOS) {
          document.body.style.position = ''
          document.body.style.width = ''
          document.body.style.height = ''
          document.body.style.overflow = ''
        }
      }
    }, [isOpen])

    const memoizedSearchDrawer = useMemo(
      () => (
        <Drawer
          isOpen={isOpen}
          onOpenChange={setIsOpen}
          placement={isIOS ? 'top' : 'bottom'}
          shouldBlockScroll
          aria-labelledby='mobile-search-drawer'
          classNames={{
            wrapper: 'rounded-none',
            base: 'm-0 rounded-none md:rounded-lg',
            body: 'p-3'
          }}
          closeButton={''}
        >
          <DrawerContent>
            <DrawerBody className='px-2 pt-10'>
              <h2 id='mobile-search-drawer' className='sr-only'>
                Поиск товаров
              </h2>
              <SearchBar autofocus={false} isMobile={isMobile} tabPlacement='bottom' isMounted={true} onSearch={() => setIsOpen(false)} />
            </DrawerBody>
          </DrawerContent>
        </Drawer>
      ),
      [isOpen]
    )

    return useMemo(
      () => (
        <div className='rounded-lg border-2 bg-default-100 p-1'>
          <button
            type='button'
            className='border-2-none flex cursor-pointer items-center gap-2 bg-transparent p-0'
            onClick={() => setIsOpen(true)}
            aria-label='Открыть поиск'
            aria-haspopup='dialog'
          >
            <SearchIcon className='w-5' aria-hidden='true' />
            <span className='text-default-900'>Поиск</span>
          </button>
          {memoizedSearchDrawer}
        </div>
      ),
      [memoizedSearchDrawer]
    )
  }

  const memoizedCartItem = useMemo(() => <CartItem />, [])
  const memoizedSearchItem = useMemo(() => <SearchItem />, [])

  const memoizedCard = useMemo(
    () => (
      <Card classNames={{ base: 'rounded-b-none border-0' }}>
        <CardBody className='px-1 pt-1'>
          <nav aria-label='Мобильная навигация'>
            <div className='flex items-center justify-center gap-2'>
              <HomeItem />
              {memoizedCartItem}
              {memoizedSearchItem}
            </div>
          </nav>
        </CardBody>
      </Card>
    ),
    [memoizedCartItem, memoizedSearchItem]
  )

  return <div className='w-full justify-center bg-white shadow-lg dark:bg-zinc-900'>{memoizedCard}</div>
}
