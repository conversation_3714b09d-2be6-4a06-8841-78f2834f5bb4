import { useCallback, useEffect, useState, memo, lazy, Suspense } from 'react'
import { Button, Image } from '@heroui/react'
import useEmblaCarousel from 'embla-carousel-react'
import type { EmblaOptionsType } from 'embla-carousel'
import { ChevronLeftIcon, ChevronRightIcon } from 'lucide-react'
import { getRtiImageUrl, getImageUrl } from '@/lib/config'

const ImageModal = lazy(() => import('./ImageModal'))

interface Props {
  product: any
  imageHeight: number
  isMobile?: boolean
}

type PropType = {
  slides: string[]
  options?: EmblaOptionsType
}

export const ImageViewer = memo(({ product, imageHeight, isMobile }: Props) => {
  const [emblaRef, emblaApi] = useEmblaCarousel()
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [scrollSnaps, setScrollSnaps] = useState<number[]>([])
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [selectedImage, setSelectedImage] = useState('')

  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const images = [
    getRtiImageUrl(`${product?.prod_img || product?.prod_analogsku}.jpg`),
    ...(Array.isArray(product.images) ? product.images.filter((i) => i.type === 'rti').map((i) => getImageUrl(i.path)) : []),
    getRtiImageUrl(`${product?.prod_type}.jpg`)
  ]

  const handleImageClick = useCallback((image: string) => {
    setSelectedImage(image)
    setIsModalOpen(true)
  }, [])

  const scrollTo = useCallback(
    (index: number) => {
      if (emblaApi) emblaApi.scrollTo(index)
    },
    [emblaApi]
  )

  const onSelect = useCallback(() => {
    if (!emblaApi) return
    setSelectedIndex(emblaApi.selectedScrollSnap())
  }, [emblaApi])

  useEffect(() => {
    if (!emblaApi) return
    onSelect()
    setScrollSnaps(emblaApi.scrollSnapList())
    emblaApi.on('select', onSelect)
    emblaApi.on('reInit', onSelect)
  }, [emblaApi, onSelect])

  return (
    <>
      {images.length > 0 && (
        <div>
          <div className='mx-auto max-w-3xl'>
            <div className='overflow-hidden' ref={emblaRef}>
              <div className='flex h-full touch-pan-y touch-pinch-zoom'>
                {images.map((image, index) => (
                  <div key={index} className='relative flex h-full w-full min-w-full flex-none items-center justify-center'>
                    <div className='flex h-full w-full items-center justify-center p-4'>
                      <Image
                        loading={index === 0 ? 'eager' : 'lazy'}
                        fetchPriority={index === 0 ? 'high' : 'auto'}
                        height={imageHeight || 250}
                        width='auto'
                        className='w-auto cursor-pointer object-contain dark:brightness-75'
                        src={image}
                        alt={`${product.prod_purpose || product.prod_sku || 'Товар'} ${product.prod_manuf || ''} ${product.prod_size || ''} - изображение ${index + 1}`}
                        onClick={() => handleImageClick(image)}
                        title={`Нажмите для увеличения изображения ${product.prod_purpose || product.prod_sku || 'товара'}`}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className='flex justify-end gap-5 px-3 md:mt-7 md:justify-between'>
              {!isMobile && (
                <div className='hidden grid-cols-2 items-center gap-2 md:grid'>
                  <Button
                    size='sm'
                    disabled={selectedIndex === 0}
                    isIconOnly
                    onPress={() => scrollTo(selectedIndex - 1)}
                    variant='flat'
                    aria-label='Предыдущее изображение'
                  >
                    <ChevronLeftIcon aria-hidden='true' />
                  </Button>
                  <Button
                    size='sm'
                    variant='flat'
                    isIconOnly
                    onPress={() => scrollTo(selectedIndex + 1)}
                    disabled={selectedIndex === scrollSnaps.length - 1}
                    aria-label='Следующее изображение'
                  >
                    <ChevronRightIcon aria-hidden='true' />
                  </Button>
                </div>
              )}

              {mounted && (
                <div className='-mr-[0.6rem] flex flex-wrap items-center justify-end gap-2'>
                  {scrollSnaps.map((_, index) => (
                    <Button
                      key={index}
                      className='px-0.5'
                      variant='bordered'
                      color={index === selectedIndex ? 'warning' : 'default'}
                      isIconOnly
                      onPress={() => scrollTo(index)}
                      aria-label={`Показать изображение ${index + 1}`}
                      aria-pressed={index === selectedIndex}
                    >
                      <Image loading='lazy' className='rounded-full' src={images[index]} alt='' aria-hidden='true' />
                    </Button>
                  ))}
                </div>
              )}
            </div>
          </div>

          {mounted && (
            <Suspense fallback={null}>
              <ImageModal isOpen={isModalOpen} onOpenChange={setIsModalOpen} selectedImage={selectedImage} />
            </Suspense>
          )}
        </div>
      )}
    </>
  )
})

ImageViewer.displayName = 'ImageViewer'
