import { <PERSON>complete, AutocompleteI<PERSON>, But<PERSON>, Divider } from '@heroui/react'
import { useAsyncList } from '@react-stately/data'
import { ClockIcon, SearchIcon, XIcon } from 'lucide-react'
import { useRef, useState, useEffect, useCallback, memo } from 'react'
import { navigate } from 'astro:transitions/client'
import { clearStoreState, updateURLParams } from '@/stores/qs'

// Интерфейс для товара
interface Product {
  prod_id: number
  prod_sku: string
  prod_img?: string
  prod_count: number
  prod_purpose?: string
  prod_size?: string
  prod_manuf?: string
  prod_analogsku?: string
  prod_type?: string
  prod_material?: string
  _formatted?: {
    prod_id?: number
    prod_purpose?: string
    prod_sku?: string
    prod_size?: string
    prod_img?: string
    prod_analogsku?: string
    prod_type?: string
    prod_material?: string
    prod_manuf?: string
    [key: string]: unknown
  }
  [key: string]: unknown
}

interface Props {
  apiUrl?: string
  boxLabel?: string
  initialSearchValue?: string
  onSearchValueChange?: (value: string) => void
  inputEndContent?: React.ReactNode
  inputStartContent?: React.ReactNode
  isMobile?: boolean
  className?: string
  size?: 'sm' | 'md' | 'lg'
  maxWidth?: string
  onSearch?: () => void
  searchMode?: 'name' | 'size'
  autofocus?: boolean
}

// Константа для ключа localStorage
const SEARCH_HISTORY_KEY = 'search_history'

// Интерфейс для элемента истории поиска
interface SearchHistoryItem {
  query: string
  timestamp: number
}

// Функция для получения истории поисков из localStorage
const getSearchHistory = (): SearchHistoryItem[] => {
  if (typeof window === 'undefined') return []

  try {
    const history = localStorage.getItem(SEARCH_HISTORY_KEY)
    return history ? JSON.parse(history) : []
  } catch (error) {
    console.error('Ошибка при получении истории поисков:', error)
    return []
  }
}

// Функция для сохранения поискового запроса в историю
const saveToSearchHistory = (query: string) => {
  if (typeof window === 'undefined' || !query || query.length < 4) return

  try {
    const history = getSearchHistory()

    // Проверяем, есть ли уже такой запрос в истории
    const existingIndex = history.findIndex(item => item.query.toLowerCase() === query.toLowerCase())

    // Если запрос уже есть, удаляем его (чтобы добавить в начало)
    if (existingIndex !== -1) {
      history.splice(existingIndex, 1)
    }

    // Добавляем новый запрос в начало массива
    history.unshift({ query, timestamp: Date.now() })

    // Ограничиваем количество элементов в истории (например, до 10)
    const limitedHistory = history.slice(0, 10)

    // Сохраняем обновленную историю
    localStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify(limitedHistory))
  } catch (error) {
    console.error('Ошибка при сохранении истории поисков:', error)
  }
}

// Функция для очистки истории поисков
const clearSearchHistory = () => {
  if (typeof window === 'undefined') return

  try {
    localStorage.removeItem(SEARCH_HISTORY_KEY)
  } catch (error) {
    console.error('Ошибка при очистке истории поисков:', error)
  }
}

export const InstantSearchAutocomplete = memo(
  ({
    apiUrl,
    boxLabel = '',
    initialSearchValue = '',
    onSearchValueChange,
    inputEndContent,
    inputStartContent,
    isMobile = false,
    className = '',
    size = 'md',
    maxWidth = '500px',
    onSearch,
    autofocus = false
  }: Props) => {
    const inputRef = useRef<HTMLInputElement>(null)
    const [isLoading, setIsLoading] = useState(false)
    const [searchHistory, setSearchHistory] = useState<SearchHistoryItem[]>([])

    // Загружаем историю поисков при монтировании компонента
    useEffect(() => {
      if (typeof window !== 'undefined') {
        setSearchHistory(getSearchHistory())
      }
    }, [])

    // Функция для подсветки совпадений
    const highlightHtml = (html: string) => {
      return html?.replace(/__ais-highlight__/g, '<span class="bg-warning-100 px-1 rounded">')?.replace(/__\/ais-highlight__/g, '</span>') || ''
    }

    // Используем useAsyncList для асинхронной фильтрации
    const list = useAsyncList<Product>({
      async load({ signal, filterText }) {
        if (!filterText || filterText.length < 2) return { items: [] }

        try {
          const searchUrl = '/api/service/instantsearch/'

          const resp = await fetch(searchUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ q: filterText }),
            signal
          })

          if (!resp.ok) return { items: [] }

          const data = await resp.json()
          return { items: data?.hits?.length ? [{}, ...data.hits] : [] }
        } catch (error) {
          // console.error('Ошибка при поиске:', error)
          return { items: [] }
        }
      }
    })

    // Обработчик полного поиска
    const handleSearch = async (searchText?: string) => {
      const query = searchText || list.filterText
      if (!query || query.length < 3) return

      setIsLoading(true)
      clearStoreState()

      // Сохраняем запрос в историю поисков
      saveToSearchHistory(query)

      // Обновляем состояние истории поисков
      setSearchHistory(getSearchHistory())

      await navigate(`/search/${encodeURIComponent(query)}`)
      setIsLoading(false)
    }

    // Функция очистки полей поиска
    const clearSearchValues = useCallback(() => {
      list.setFilterText('')
    }, [list])

    useEffect(() => {
      if (typeof window === 'undefined') return

      const checkAndClear = () => {
        const currentPath = window.location.pathname
        if (!currentPath.includes('/search/')) {
          clearSearchValues()
        }
      }

      document.addEventListener('astro:page-load', checkAndClear)
      return () => {
        document.removeEventListener('astro:page-load', checkAndClear)
      }
    }, [clearSearchValues])

    // Синхронизация внешнего initialSearchValue с внутренним состоянием
    useEffect(() => {
      if (initialSearchValue && initialSearchValue !== '') {
        list.setFilterText(initialSearchValue)
      }
    }, [initialSearchValue, list])

    return (
      <div className={`relative flex items-center rounded-lg bg-default-50 dark:bg-default-100 ${className}`}>
        <div className='flex w-full items-center'>
          <Autocomplete
            defaultItems={[]}
            inputValue={list.filterText}
            onInputChange={(value) => {
              list.setFilterText(value)
              onSearchValueChange?.(value)
            }}
            isLoading={list.isLoading || isLoading}
            items={list.filterText.length >= 2 ? list.items : (searchHistory.length > 0 ? [...searchHistory] as any[] : [])}
            placeholder='Поиск'
            aria-label='Поиск по каталогу'
            size={size}
            isClearable
            ref={inputRef}
            allowsCustomValue
            autoFocus={autofocus}
            onFocus={() => {
              // Обновляем историю поисков при фокусе на поле ввода
              setSearchHistory(getSearchHistory())
            }}
            classNames={{
              base: 'w-full',
              listboxWrapper: isMobile ? '' : 'max-h-[50vh]',
              selectorButton: 'hidden'
            }}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                  handleSearch()
  
              }
            }}
            listboxProps={{
              emptyContent: list.filterText.length > 1 ? 'Ничего не найдено' : 'Введите минимум 2 символа',
              classNames: {
                base: 'w-full p-0 scrollbar-thin scrollbar-track-zinc-100 scrollbar-thumb-zinc-300'
              },
              topContent: searchHistory.length > 0 && list.filterText.length < 2 ? (
                <div className="py-1 px-2">
                  <div className="flex items-center justify-between">
                    <div className="text-xs text-default-500">История поисков</div>
                    <Button
                      size="sm"
                      variant="light"
                      className="text-xs text-default-500"
                      onPress={() => {
                        clearSearchHistory();
                        setSearchHistory([]);
                      }}
                    >
                      <XIcon size={14} />
                      <span>Очистить</span>
                    </Button>
                  </div>
                  <Divider className="my-1" />
                </div>
              ) : undefined
            }}
            startContent={inputStartContent || undefined}
            endContent={inputEndContent}
            popoverProps={{
              classNames: {
                base: `${isMobile ? 'w-[330px]' : 'w-full'}`,
                content: 'p-0'
              }
            }}
          >
            {(item: any) => {
              // Проверяем, является ли элемент историей поиска
              if (item && 'query' in item && typeof item.query === 'string') {
                return (
                  <AutocompleteItem
                    key={`history-${item.timestamp}`}
                    textValue={item.query}
                    onPress={() => {
                      // Устанавливаем значение поиска и выполняем поиск
                      // list.setFilterText(item.query)
                      handleSearch(item.query)
                    }}
                    classNames={{
                      base: 'py-2 px-3 data-[hover=true]:bg-default-100'
                    }}
                  >
                    <div className='flex w-full items-center gap-2 text-sm'>
                      <ClockIcon size={16} className='text-default-400' />
                      <span>{item.query}</span>
                    </div>
                  </AutocompleteItem>
                )
              }

              // Рендерим товар
              return (
                <AutocompleteItem
                  key={String(item.prod_id)}
                  onPress={() => {
                    window.open(`/catalog/product/${item.prod_id}`, '_blank')
                    list.setFilterText('')
                  }}
                  textValue={`${item.prod_purpose || item._formatted?.prod_purpose || ''} ${item.prod_sku || item._formatted?.prod_sku || ''}`}
                  classNames={{
                    base: 'py-2 px-3 data-[hover=true]:bg-default-100'
                  }}
                >
                  <a
                    href={`/catalog/product/${item.prod_id}`}
                    target='_blank'
                    onClick={(e) => {
                      list.setFilterText('')
                      e.stopPropagation()
                    }}
                    aria-label='Открыть в новом окне'
                    rel='noreferrer'
                  >
                    <div className='sm:tex-sm flex w-full cursor-pointer items-center justify-between text-xs'>
                      <div className='flex gap-1 truncate'>
                        <div
                          className='max-w-28 overflow-hidden truncate'
                          dangerouslySetInnerHTML={{
                            __html: highlightHtml(item._formatted?.prod_purpose || item.prod_purpose || '')
                          }}
                          title={item._formatted?.prod_purpose || item.prod_purpose || ''}
                        />
                        <div
                          className=''
                          dangerouslySetInnerHTML={{
                            __html: highlightHtml(item._formatted?.prod_sku || item.prod_sku || '')
                          }}
                        />
                        {(item._formatted?.prod_analogsku || item.prod_analogsku) && (
                          <div className='text-default-600'>
                            <div
                              dangerouslySetInnerHTML={{
                                __html: highlightHtml(item._formatted?.prod_analogsku || item.prod_analogsku || '')
                              }}
                            />
                          </div>
                        )}
                        {(item._formatted?.prod_size || item.prod_size) && (
                          <div className='font-semibold text-default-900'>
                            <div
                              dangerouslySetInnerHTML={{
                                __html: highlightHtml(item._formatted?.prod_size || item.prod_size || '')
                              }}
                            />
                          </div>
                        )}
                        {(item.prod_manuf || item._formatted?.prod_manuf) && (
                          <div className='ml-1 text-default-600'>{item.prod_manuf || item._formatted?.prod_manuf}</div>
                        )}
                      </div>
                    </div>
                  </a>
                </AutocompleteItem>
              )
            }}
          </Autocomplete>
          {boxLabel && <div className='mt-1 text-xs text-default-500'>{boxLabel}</div>}
        </div>
        <Button onPress={() => handleSearch()} size={size} isIconOnly className='ml-2' aria-label='Найти' isLoading={isLoading}>
          <SearchIcon size={18} />
        </Button>
      </div>
    )
  }
)
