import { useState, useEffect, useCallback, useMemo, memo } from 'react'
import { Card, CardBody, Image, Input, Link, <PERSON>Footer, Alert } from '@heroui/react'
import { ExternalLinkIcon, SearchIcon } from 'lucide-react'
// import { motion } from 'framer-motion'
import { QtyInput } from './QtyInput'
import { getRtiImageUrl } from '@/lib/config'

interface Product {
  prod_id: number
  prod_sku: string
  prod_img: string
  prod_count: number
  _formatted: {
    prod_purpose: string
    prod_analogs: string
    prod_note: string
    prod_analogsku: string
    prod_size: string
    prod_type: string
    prod_material: string
    [key: string]: any
  }
  [key: string]: any
}

interface Props {
  apiUrl?: string
  boxLabel?: string
  initialSearchValue?: string
  onSearchValueChange?: (value: string) => void
  inputEndContent?: React.ReactNode
  inputStartContent?: React.ReactNode
}

const Hit = memo(({ item }: { item: Product }) => {
  const highlightHtml = (html: string) => {
    return html?.replace(/__ais-highlight__/g, '<span class="bg-warning-100 px-1 rounded">')?.replace(/__\/ais-highlight__/g, '</span>') || ''
  }

  return (
    <div className='animate-fade-in my-5 transition-all duration-300 ease-out'>
      <Card className='my-5 border dark:border-default-200' as='div' shadow='none' fullWidth isFooterBlurred>
        <CardBody className='p-0'>
          <div className='relative z-0 flex justify-center'>
            <Card isFooterBlurred className='max-w-sm border-none' radius='lg'>
              <Image
                shadow='lg'
                width={250}
                height={210}
                fallbackSrc={getRtiImageUrl(`${item.prod_img || item._formatted.prod_img || item.prod_analogsku}.jpg`)}
                className='z-0 border dark:brightness-75'
                removeWrapper
                loading='lazy'
                src={getRtiImageUrl(`${item.prod_img || item._formatted.prod_img || item.prod_analogsku}.jpg`)}
                alt={item.prod_sku}
              />
              <CardFooter className='absolute bottom-1 z-10 ml-1 justify-center overflow-hidden rounded-large border-1 border-white/20 py-1 shadow-small before:rounded-xl before:bg-white/10'>
                <div className='flex items-center justify-center gap-2'>
                  <div className={`h-3 w-3 rounded-full ${item.prod_count > 0 ? 'bg-success-500' : 'bg-default-500'}`} />
                  <span className='text-sm text-default-600'>
                    {item.prod_count > 0 ? 'В наличии' : 'Предзаказ'}
                    {item.prod_count > 0 && <span> {item.prod_count} шт</span>}
                  </span>
                </div>
              </CardFooter>
            </Card>

            <div className='p-4'>
              <div className='mb-4 gap-2 text-sm'>
                <div className='flex gap-2 rounded-md p-1 px-2 odd:bg-default-100'>
                  <div className='shrink-0 font-semibold'>Назначение:</div>
                  <div dangerouslySetInnerHTML={{ __html: highlightHtml(item._formatted.prod_purpose) }} />
                </div>

                <div className='flex gap-2 rounded-md p-1 px-2 odd:bg-default-100'>
                  <div className='shrink-0 font-semibold'>Артикул:</div>
                  <div>
                    <span dangerouslySetInnerHTML={{ __html: highlightHtml(item._formatted.prod_sku) }} /> /
                    <span dangerouslySetInnerHTML={{ __html: highlightHtml(item._formatted.prod_analogsku) }} />
                  </div>
                </div>

                <div className='flex gap-2 rounded-md p-1 px-2 odd:bg-default-100'>
                  <div className='shrink-0 font-semibold'>Размер:</div>
                  <div dangerouslySetInnerHTML={{ __html: highlightHtml(item._formatted.prod_size) }} />
                </div>

                <div className='flex gap-2 rounded-md p-1 px-2 odd:bg-default-100'>
                  <div className='shrink-0 font-semibold'>Тип/Материал:</div>
                  <div>
                    <span dangerouslySetInnerHTML={{ __html: highlightHtml(item._formatted.prod_type) }} /> /
                    <span dangerouslySetInnerHTML={{ __html: highlightHtml(item._formatted.prod_material) }} />
                  </div>
                </div>
              </div>

              <div className='flex justify-end gap-2'>
                <QtyInput label='Купить' size='md' product={{ ...item, prod_id: Number(item._formatted?.prod_id || item.prod_id) }} />
                <Link
                  className='gap-2 rounded-lg bg-default-200 p-2 text-sm dark:bg-default-100'
                  target='_blank'
                  color='foreground'
                  href={`/catalog/product/${item._formatted?.prod_id || item.prod_id}`}
                >
                  <span>Подробнее</span>
                  <ExternalLinkIcon className='w-5' />
                </Link>
                {/* <QuickViewButton product={item} /> */}
              </div>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  )
})

export const InstantSearchBox = ({ apiUrl, boxLabel = '', initialSearchValue = '', onSearchValueChange, inputEndContent, inputStartContent }: Props) => {
  const [query, setQuery] = useState(initialSearchValue)
  const [searchResults, setSearchResults] = useState<Product[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const searchUrl = useMemo(() => {
    const baseUrl = apiUrl || (import.meta.env.DEV ? import.meta.env.PUBLIC_API_URL_DEV : import.meta.env.PUBLIC_API_URL)
    return `${baseUrl}/service/instantsearch/`
  }, [apiUrl])

  const handleSearch = useCallback(
    async (searchQuery: string) => {
      if (!searchQuery) {
        setSearchResults([])
        return
      }
      try {
        setIsLoading(true)
        setError(null)

        const response = await fetch(searchUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ q: searchQuery })
        })

        if (!response.ok) throw new Error('Ошибка поиска')

        const data = await response.json()
        setSearchResults(data?.hits)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Неизвестная ошибка')
      } finally {
        setIsLoading(false)
      }
    },
    [searchUrl]
  )

  function onValueChangeHandler(value: string) {
    setQuery(value)
    onSearchValueChange?.(value)
  }

  useEffect(() => {
    const timer = setTimeout(() => handleSearch(query), 200)
    return () => clearTimeout(timer)
  }, [query, handleSearch])

  return (
    <div className='relative mx-auto w-full'>
      <div className='relative flex gap-2 rounded-lg'>
        <Input
          value={query}
          placeholder='Поиск'
          autoFocus
          // label="Поиск по каталогу"
          labelPlacement='outside'
          size='lg'
          autoCapitalize='sentences'
          variant='flat'
          // onChange={(e) => setQuery(e.target.value)}
          onValueChange={onValueChangeHandler}
          isClearable
          // endContent={inputEndContent}
          startContent={inputStartContent || <SearchIcon />}
        />
        <div>{inputEndContent}</div>
      </div>

      {error && (
        <Alert variant='flat' color='danger'>
          Ошибка загрузки
        </Alert>
      )}

      <div className='mt-4 h-[70vh] overflow-auto scrollbar-thin scrollbar-track-default-100 scrollbar-thumb-default-300'>
        <div>{boxLabel}</div>
        <div className='relative w-full'>
          {searchResults.map((item, index) => {
            return (
              <div
                key={item.prod_id || index}
                className='animate-slide-up w-full'
                style={{
                  animationDelay: `${index * 50}ms`
                }}
              >
                <Hit item={item} />
              </div>
            )
          })}
          {searchResults.length === 0 && !isLoading && query && <div className='py-2 text-center text-sm text-default-700'>Нет результатов</div>}
        </div>

        {isLoading && (
          <div className='space-y-4 p-4'>
            {Array(3)
              .fill(0)
              .map((_, idx) => (
                <Card key={idx} className='animate-pulse'>
                  <CardBody className='h-24 rounded-lg bg-default-100' />
                </Card>
              ))}
          </div>
        )}
      </div>
    </div>
  )
}
