import { Button, Modal, ModalBody, <PERSON>dal<PERSON>ontent, <PERSON>dal<PERSON>ooter } from '@heroui/react'
import { useState } from 'react'
import { ProductPreorder } from './ProductPreorder'
import { ShoppingCartIcon } from 'lucide-react'

export const PreorderButton = ({ product, label = 'Оформить предзаказ', mini = false, size = 'sm', isTabler }) => {
  const [isPreorderModalOpen, setPreorderModalOpen] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState(product)

  function onPressHandler(e) {
    setSelectedProduct(product)
    setPreorderModalOpen(true)
  }
  return (
    <>
      {isTabler ? (
        <Button variant='bordered' onPress={(e) => onPressHandler(e)} isIconOnly size='sm' className='text-base font-semibold'>
          ?
        </Button>
      ) : mini ? (
        <Button variant='flat' size={size} color='warning' onPress={(e) => onP<PERSON><PERSON><PERSON><PERSON>(e)}>
          <div className='flex items-center gap-3 font-bold'>{<ShoppingCartIcon />}</div>
        </Button>
      ) : (
        <Button className='text-wrap' size='sm' variant='bordered' onPress={(e) => onPressHandler(e)}>
          {label}
        </Button>
      )}

      <Modal
        isDismissable={false}
        size='3xl'
        isOpen={isPreorderModalOpen}
        onOpenChange={setPreorderModalOpen}
        closeButton={<span />}
      >
        <ModalContent>
          {(onClose) => (
            <ModalBody>
              <ProductPreorder product={selectedProduct} />
              <ModalFooter>
                <Button variant='flat' color='danger' onPress={onClose}>
                  Закрыть
                </Button>
              </ModalFooter>
            </ModalBody>
          )}
        </ModalContent>
      </Modal>
    </>
  )
}
