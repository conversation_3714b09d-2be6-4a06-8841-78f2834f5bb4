import { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from 'react'
import { trpc } from '@/trpc'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ooter, <PERSON>ton, Image, Tooltip } from '@heroui/react'
import { EyeIcon, ExternalLinkIcon } from 'lucide-react'
import { getRtiImageUrl } from '@/lib/config'

// Объявляем типы для imagemapper
declare module '@overlapmedia/imagemapper' {
  export const view: (id: string, options: { width: number; height: number }) => ImageMapperView
}

interface Component {
  id: string
  productId: number
  title: string
}

interface Schema {
  img: string
  schema: {
    components: Component[]
  }
}

interface Product {
  prod_id: number
  prod_purpose: string
  prod_sku: string
  prod_type: string
  prod_size: string
  prod_img: string
  prod_analogsku: string
}

interface Props {
  options?: {
    width?: number
    height?: number
  }
  data: Schema
  product: Product
  isMobile?: boolean
}

interface ImageMapperView {
  loadImage: (src: string, width: number, height: number) => void
  import: (data: string) => void
  getComponentById: (id: string) => {
    element: SVGGraphicsElement // Изменили тип на SVGGraphicsElement
    clickHandler?: (e: Event) => void
  } | null
}

export const SchemaViewer = ({ options = {}, data, product, isMobile }: Props) => {
  const elementRef = useRef<SVGSVGElement>(null)
  const viewInstanceRef = useRef<ImageMapperView | null>(null)
  const [width = 700, height = 500] = [options.width, options.height]

  const relatedProductsIds = useMemo(() => data?.schema?.components?.map((i) => i?.productId).filter(Boolean) || [], [data?.schema?.components])

  const [relatedProducts, setRelatedProducts] = useState<Product[]>([])
  const [activeRelatedId, setActiveRelatedId] = useState<number | null>(null)

  const handleComponentClick = useCallback(
    (comp: Component) => {
      if (comp.productId !== product?.prod_id) {
        setActiveRelatedId(comp.productId)
        const productCard = document.querySelector(`[data-product-id="${comp.productId}"]`)
        productCard?.scrollIntoView({ behavior: 'smooth', block: 'center' })
      } else {
        setActiveRelatedId(null)
      }
    },
    [product?.prod_id]
  )

  const productLoader = useCallback(async (id: number) => {
    return await trpc.products.getProductDataById.query(id)
  }, [])

  useEffect(() => {
    if (!elementRef.current || !elementRef.current.id) return

    const initializeSchema = async () => {
      try {
        // Импортируем модуль напрямую с указанием типа
        const { view } = (await import('@overlapmedia/imagemapper')) as typeof import('@overlapmedia/imagemapper')

        const svgId = elementRef.current?.id
        if (!svgId) return

        const myView = view(svgId, {
          width: 800,
          height: 400
        })

        viewInstanceRef.current = myView
        myView.loadImage('', 700, 500)

        const imageElement = document.querySelector(`#${elementRef.current?.id} image`)
        if (imageElement) {
          imageElement.setAttribute('href', String(data?.img))
          imageElement.classList.add('dark:brightness-75')
        }

        myView.import(JSON.stringify(data?.schema))

        for (const comp of data.schema?.components || []) {
          const svg = myView.getComponentById(comp.id)
          if (!svg) continue

          const bbox = svg.element.getBBox()
          const tooltipTrigger = document.querySelector(`[data-component-id="${comp.id}"]`) as HTMLElement

          if (tooltipTrigger) {
            tooltipTrigger.classList.add('absolute', 'bg-transparent')
            tooltipTrigger.style.left = `${bbox.x}px`
            tooltipTrigger.style.top = `${bbox.y}px`
            tooltipTrigger.style.width = `${bbox.width}px`
            tooltipTrigger.style.height = `${bbox.height}px`
            tooltipTrigger.setAttribute('data-related-id', comp.productId.toString())

            const clickHandler = (e: Event) => {
              e.preventDefault()
              e.stopPropagation()
              handleComponentClick(comp)
            }

            tooltipTrigger.addEventListener('click', clickHandler)
            svg.clickHandler = clickHandler
          }

          if (comp.productId !== product?.prod_id) {
            svg.element.classList.add('stroke-green-400', 'stroke-4', 'fill-green-100')
          } else {
            svg.element.classList.add('stroke-yellow-400', 'stroke-4', 'fill-yellow-100')
          }
        }
      } catch (error) {
        console.error('Schema initialization error:', error)
      }
    }

    initializeSchema()

    return () => {
      if (viewInstanceRef.current) {
        for (const comp of data.schema?.components ?? []) {
          const tooltipTrigger = document.querySelector(`[data-component-id="${comp.id}"]`)
          const svg = viewInstanceRef.current.getComponentById(comp.id)
          if (tooltipTrigger && svg?.clickHandler) {
            tooltipTrigger.removeEventListener('click', svg.clickHandler)
          }
        }
      }
    }
  }, [elementRef, data, product, handleComponentClick])

  useEffect(() => {
    if (relatedProductsIds.length > 0) {
      Promise.all(relatedProductsIds.map(productLoader)).then(setRelatedProducts).catch(console.error)
    }
  }, [relatedProductsIds, productLoader])

  useEffect(() => {
    if (viewInstanceRef.current && data.schema?.components) {
      for (const comp of data.schema.components as Component[]) {
        const svgComponent = viewInstanceRef.current?.getComponentById(comp.id)
        if (svgComponent?.element) {
          if (comp.productId === activeRelatedId) {
            svgComponent.element.classList.add('stroke-yellow-800')
          } else {
            svgComponent.element.classList.remove('stroke-yellow-800')
          }
        }
      }
    }
  }, [activeRelatedId, data.schema?.components])

  const onRelatedClickHandler = useCallback((prod_id: number): void => {
    setActiveRelatedId(prod_id)
    const schemaContainer = document.querySelector('#schemaView')
    schemaContainer?.scrollIntoView({ behavior: 'smooth', block: 'center' })
  }, [])

  const renderTooltip = useCallback(
    (comp: Component) => {
      const isOpen = comp.productId === product?.prod_id || comp.productId === activeRelatedId
      const color = comp.productId === activeRelatedId ? 'warning' : 'default'
      const content = (
        <div>
          <span>{comp.title}</span>
          {comp.productId === product?.prod_id && <span className='text-xs font-semibold'>(Этот товар)</span>}
        </div>
      )

      return (
        <Tooltip key={comp.id} shouldFlip showArrow shouldCloseOnBlur isOpen={isOpen} shadow='lg' closeDelay={0} delay={0} color={color} content={content}>
          <div data-component-id={comp.id} className='cursor-pointer bg-transparent' />
        </Tooltip>
      )
    },
    [activeRelatedId, product]
  )

  return (
    <div className='relative'>
      <div className='schema-container'>
        <svg
          id='schemaView'
          ref={elementRef}
          version='1.1'
          xmlns='http://www.w3.org/2000/svg'
          width={width}
          height={height}
          viewBox={`0, 0, ${width}, ${height}`}
          preserveAspectRatio='xMinYMin'
        />
        {!isMobile ? <div className='absolute inset-0'>{data.schema?.components?.map(renderTooltip)}</div> : ''}
      </div>
      {relatedProductsIds.length > 0 && (
        <div className='mt-6'>
          <div className='mb-4 text-center font-semibold'>Товары на схеме</div>
          <div className='flex flex-wrap justify-start gap-4'>
            {relatedProducts.map((p) => (
              <Card
                key={p.prod_id}
                as='div'
                isPressable
                isBlurred
                isFooterBlurred
                className={`${activeRelatedId === p.prod_id ? 'border-2 border-warning-300 bg-warning-50' : ''} _w-full h-[200px] min-w-64 cursor-pointer`}
                data-product-id={p.prod_id}
                onPress={() => onRelatedClickHandler(p.prod_id)}
              >
                <CardHeader className='absolute top-1 z-10 flex-col items-start gap-2 bg-white/90'>
                  <span className='font-medium text-default-800'>
                    {p.prod_purpose} {p.prod_sku}
                  </span>
                  <p className='text-sm text-default-600'>
                    {p.prod_type} {p.prod_size}
                  </p>
                </CardHeader>
                <Image
                  removeWrapper
                  alt={p.prod_purpose}
                  className='z-0 h-full w-full -translate-y-6 scale-95 object-cover'
                  src={getRtiImageUrl(`${p.prod_img || p.prod_analogsku}.jpg`)}
                />
                <CardFooter className='absolute bottom-0 z-10 justify-between border-t-1 border-default-100/50 bg-white/80'>
                  <div>
                    <p className='text-sm text-default-800'>{p.prod_analogsku}</p>
                  </div>
                  <div className='flex gap-2'>
                    <Button as='button' onPress={() => onRelatedClickHandler(p.prod_id)} className='text-xs' color='default' size='sm'>
                      <EyeIcon className='w-4' />
                    </Button>
                    <Button as='a' href={`/catalog/product/${p.prod_id}`} className='text-xs' color='default' size='sm' target='_blank'>
                      Перейти <ExternalLinkIcon className='w-4' />
                    </Button>
                  </div>
                </CardFooter>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
