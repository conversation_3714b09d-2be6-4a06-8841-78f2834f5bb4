import React, { useState, useRef, useEffect } from 'react'
import { <PERSON>ton, Card, CardBody, Textarea, useDisclosure, Avatar, Chip, Divider, Spinner, Image, Badge, CardFooter, Link } from '@heroui/react'
import { ExternalLinkIcon, SendIcon, ShoppingCartIcon, RotateCcwIcon } from 'lucide-react'
import { trpc, trpcReact } from '@/trpc'
import { TrpcReactWrapper } from './TrcpReactWrapper'
import { QtyInput } from './QtyInput'
import { getRtiImageUrl } from '@/lib/config'

interface Product {
  id: string
  name: string
  price: number
  description: string
  category: string
  inStock: boolean
  image?: string
}

interface ChatMessage {
  id: string
  text: string
  isUser: boolean
  timestamp: Date
  products?: Product[]
}

interface ApiResponse {
  response: string
  products?: Product[]
  threadId: string
  resourceId: string
}

const highlightHtml = (html: string) => {
  return html?.replace(/_shl_/g, '<span class="bg-warning-100 px-1 rounded">')?.replace(/_ehl_/g, '</span>') || ''
}

export const SearchAIChat = () => {
  return (
    <TrpcReactWrapper>
      <Chat />
    </TrpcReactWrapper>
  )
}

const ProductCard = ({ product: item }) => (
  <div className='animate-fade-in my-5 transition-all duration-300 ease-out'>
    <Card className='my-5 border dark:border-default-200' as='div' shadow='none' fullWidth isFooterBlurred>
      <CardBody className='p-0'>
        <div className='relative z-0 flex justify-center'>
          <Card isFooterBlurred className='max-w-sm border-none' radius='lg'>
            <Image
              shadow='lg'
              width={250}
              height={210}
              fallbackSrc={getRtiImageUrl(`${item.prod_img || item._formatted.prod_img || item.prod_analogsku}.jpg`)}
              className='z-0 border dark:brightness-75'
              removeWrapper
              loading='lazy'
              src={getRtiImageUrl(`${item.prod_img || item._formatted.prod_img || item.prod_analogsku}.jpg`)}
              alt={item.prod_sku}
            />
            <CardFooter className='absolute bottom-1 z-10 ml-1 justify-center overflow-hidden rounded-large border-1 border-white/20 py-1 shadow-small before:rounded-xl before:bg-white/10'>
              <div className='flex items-center justify-center gap-2'>
                <div className={`h-3 w-3 rounded-full ${item.prod_count > 0 ? 'bg-success-500' : 'bg-default-500'}`} />
                <span className='text-sm text-default-600'>
                  {item.prod_count > 0 ? 'В наличии' : 'Предзаказ'}
                  {item.prod_count > 0 && <span> {item.prod_count} шт</span>}
                </span>
              </div>
            </CardFooter>
          </Card>

          <div className='p-4'>
            <div className='mb-4 gap-2 text-sm'>
              <div className='flex gap-2 rounded-md p-1 px-2 odd:bg-default-100'>
                <div className='shrink-0 font-semibold'>Назначение:</div>
                <div dangerouslySetInnerHTML={{ __html: highlightHtml(item._formatted.prod_purpose) }} />
              </div>

              <div className='flex gap-2 rounded-md p-1 px-2 odd:bg-default-100'>
                <div className='shrink-0 font-semibold'>Артикул:</div>
                <div>
                  <span dangerouslySetInnerHTML={{ __html: highlightHtml(item._formatted.prod_sku) }} /> /
                  <span dangerouslySetInnerHTML={{ __html: highlightHtml(item._formatted.prod_analogsku) }} />
                </div>
              </div>

              <div className='flex gap-2 rounded-md p-1 px-2 odd:bg-default-100'>
                <div className='shrink-0 font-semibold'>Размер:</div>
                <div dangerouslySetInnerHTML={{ __html: highlightHtml(item._formatted.prod_size) }} />
              </div>

              <div className='flex gap-2 rounded-md p-1 px-2 odd:bg-default-100'>
                <div className='shrink-0 font-semibold'>Тип/Материал:</div>
                <div>
                  <span dangerouslySetInnerHTML={{ __html: highlightHtml(item._formatted.prod_type) }} /> /
                  <span dangerouslySetInnerHTML={{ __html: highlightHtml(item._formatted.prod_material) }} />
                </div>
              </div>
            </div>

            <div className='flex justify-end gap-2'>
              <QtyInput label='Купить' size='md' product={{ ...item, prod_id: Number(item._formatted?.prod_id || item.prod_id) }} />
              <Link
                className='gap-2 rounded-lg bg-default-200 p-2 text-sm dark:bg-default-100'
                target='_blank'
                color='foreground'
                href={`/catalog/product/${item._formatted?.prod_id || item.prod_id}`}
              >
                <span>Подробнее</span>
                <ExternalLinkIcon className='w-5' />
              </Link>
            </div>
          </div>
        </div>
      </CardBody>
    </Card>
  </div>
)

const MessageBubble = ({ response }) => (
  <div className={`flex ${response.isUser ? 'justify-end' : 'justify-start'} mb-4`}>
    <div className={`flex max-w-[80%] items-start gap-2 ${response.isUser ? 'flex-row-reverse' : 'flex-row'}`}>
      <Avatar
        size='sm'
        name={response.isUser ? 'Вы' : 'ИИ'}
        color={response.isUser ? 'primary' : 'secondary'}
        className='flex-shrink-0'
      />
      <Card className={response.isUser ? 'bg-warning-100' : 'bg-default-100'}>
        <CardBody className='p-3'>
          <p className='text-sm'>{response.text}</p>
          <p className='mt-2 text-xs opacity-70'>
            {response.timestamp.toLocaleTimeString('ru-RU', {
              hour: '2-digit',
              minute: '2-digit'
            })}
          </p>
        </CardBody>
      </Card>
    </div>
  </div>
)

const Chat = () => {
  const { isPending: isLoading, mutateAsync: sendMessage } = trpcReact.services.aiChat.useMutation()
  const { isPending: isGeneratingThread, mutateAsync: generateThread } = trpcReact.services.generateChatThread.useMutation()
  const { isPending: isLoadingHistory, data: historyData, refetch: refetchHistory } = trpcReact.services.getChatHistory.useQuery(
    { threadId: '' },
    { enabled: false }
  )
  const { isPending: isDeletingHistory, mutateAsync: deleteHistory } = trpcReact.services.deleteChatHistory.useMutation()

  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      text: 'Привет! Я помогу вам найти резинотехнические изделия и сальники для вашего автомобиля. Что вы ищете?',
      isUser: false,
      timestamp: new Date()
    }
  ])
  const [inputValue, setInputValue] = useState('')
  const [currentThreadId, setCurrentThreadId] = useState<string | null>(null)
  const [isInitialized, setIsInitialized] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // Инициализация чата при загрузке компонента
  useEffect(() => {
    const initializeChat = async () => {
      try {
        // Проверяем, есть ли сохраненный threadId в localStorage
        const savedThreadId = localStorage.getItem('chatThreadId')

        if (savedThreadId) {
          // Пытаемся загрузить историю
          try {
            const history = await trpc.services.getChatHistory.query({ threadId: savedThreadId })

            if (history.success && history.messages?.uiMessages?.length > 0) {
              // Преобразуем историю в формат компонента
              const convertedMessages: ChatMessage[] = history.messages.uiMessages.map((msg, index) => ({
                id: msg.id || `${index}`,
                text: msg.content,
                isUser: msg.role === 'user',
                timestamp: new Date(msg.createdAt),
                products: msg.products || undefined
              }))

              setMessages(convertedMessages)
              setCurrentThreadId(savedThreadId)
              setIsInitialized(true)
              return
            }
          } catch (error) {
            console.warn('Не удалось загрузить историю чата:', error)
            // Удаляем неработающий threadId
            localStorage.removeItem('chatThreadId')
          }
        }

        // Если истории нет или она не загрузилась, создаем новый тред
        const { threadId } = await trpc.services.generateChatThread.query()
        setCurrentThreadId(threadId)
        localStorage.setItem('chatThreadId', threadId)
        setIsInitialized(true)

      } catch (error) {
        console.error('Ошибка инициализации чата:', error)
        setIsInitialized(true) // Все равно помечаем как инициализированный
      }
    }

    initializeChat()
  }, [])

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading || !isInitialized) return

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      text: inputValue,
      isUser: true,
      timestamp: new Date()
    }

    setMessages((prev) => [...prev, userMessage])
    const messageText = inputValue
    setInputValue('')

    try {
      const response = await sendMessage({
        message: messageText,
        threadId: currentThreadId || undefined
      })

      // Обновляем threadId если он изменился
      if (response.threadId && response.threadId !== currentThreadId) {
        setCurrentThreadId(response.threadId)
        localStorage.setItem('chatThreadId', response.threadId)
      }

      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: response.response,
        isUser: false,
        timestamp: new Date(),
        products: response.products
      }

      setMessages((prev) => [...prev, aiMessage])
    } catch (error) {
      console.error('Ошибка отправки сообщения:', error)
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: 'Извините, произошла ошибка. Попробуйте еще раз.',
        isUser: false,
        timestamp: new Date()
      }
      setMessages((prev) => [...prev, errorMessage])
    }
  }

  const handleNewChat = async () => {
    try {
      const { threadId } = await trpc.services.generateChatThread.query()
      setCurrentThreadId(threadId)
      localStorage.setItem('chatThreadId', threadId)

      // Сбрасываем сообщения к начальному состоянию
      setMessages([
        {
          id: '1',
          text: 'Привет! Я помогу вам найти резинотехнические изделия и сальники для вашего автомобиля. Что вы ищете?',
          isUser: false,
          timestamp: new Date()
        }
      ])
    } catch (error) {
      console.error('Ошибка создания нового чата:', error)
    }
  }

  const handleClearHistory = async () => {
    if (!currentThreadId) return

    try {
      await deleteHistory({ threadId: currentThreadId })
      localStorage.removeItem('chatThreadId')

      // Создаем новый чат
      await handleNewChat()
    } catch (error) {
      console.error('Ошибка очистки истории:', error)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  // Показываем загрузку пока чат не инициализирован
  if (!isInitialized) {
    return (
      <div className='flex h-96 items-center justify-center'>
        <div className='flex items-center gap-2'>
          <Spinner size='sm' color='primary' />
          <span className='text-sm text-default-500'>Инициализация чата...</span>
        </div>
      </div>
    )
  }

  return (
    <>
      <div className=''>
        {/* Заголовок с кнопками управления */}
        <div className='flex items-center justify-between border-b p-4'>
          <div className='flex items-center gap-2'>
            <h3 className='text-lg font-semibold'>AI Чат-помощник</h3>
            {currentThreadId && (
              <Chip size='sm' variant='flat' color='primary'>
                ID: {currentThreadId.slice(-8)}
              </Chip>
            )}
          </div>
          <div className='flex gap-2'>
            <Button
              size='sm'
              variant='flat'
              color='warning'
              onPress={handleNewChat}
              isLoading={isGeneratingThread}
              startContent={<RotateCcwIcon className='h-4 w-4' />}
            >
              Новый чат
            </Button>
            <Button
              size='sm'
              variant='flat'
              color='danger'
              onPress={handleClearHistory}
              isLoading={isDeletingHistory}
              isDisabled={!currentThreadId}
            >
              Очистить историю
            </Button>
          </div>
        </div>

        {/* Область сообщений */}
        <div className='flex-1 space-y-2 overflow-y-auto px-4 py-4' style={{ maxHeight: '60vh' }}>
          {messages.map((response) => (
            <div key={response.id}>
              <MessageBubble response={response} />

              {/* Товары */}
              {response.products && response.products.length > 0 && (
                <div className='mb-4 ml-12 space-y-3'>
                  <Divider />
                  <div className='grid gap-3'>
                    {response.products.map((product, index) => (
                      <ProductCard key={product?.prod_id || index} product={product} />
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}

          {/* Индикатор загрузки */}
          {isLoading && (
            <div className='mb-4 flex justify-start'>
              <div className='flex items-start gap-2'>
                <Avatar size='sm' name='ИИ' color='secondary' className='flex-shrink-0' />
                <Card className='bg-default-100'>
                  <CardBody className='p-3'>
                    <div className='flex items-center gap-2'>
                      <Spinner size='sm' color='primary' />
                      <span className='text-sm text-default-500'>Печатает...</span>
                    </div>
                  </CardBody>
                </Card>
              </div>
            </div>
          )}

          {/* Индикатор загрузки истории */}
          {isLoadingHistory && (
            <div className='mb-4 flex justify-center'>
              <div className='flex items-center gap-2'>
                <Spinner size='sm' color='primary' />
                <span className='text-sm text-default-500'>Загрузка истории...</span>
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>

        <Divider />

        {/* Поле ввода */}
        <div className='p-4'>
          <div className='flex gap-2'>
            <Textarea
              value={inputValue}
              onValueChange={setInputValue}
              onKeyDown={handleKeyPress}
              placeholder='Напишите, что вы ищете...'
              minRows={1}
              maxRows={3}
              className='flex-1'
              isDisabled={isLoading || !isInitialized}
              variant='bordered'
            />
            <Button
              isIconOnly
              color='primary'
              onPress={handleSendMessage}
              isDisabled={!inputValue.trim() || isLoading || !isInitialized}
              isLoading={isLoading}
              className='self-end'
            >
              <SendIcon className='h-5 w-5' />
            </Button>
          </div>

          {/* Статус подключения */}
          <div className='mt-2 flex items-center justify-between text-xs text-default-500'>
            <span>
              {currentThreadId ? `Чат активен (ID: ${currentThreadId.slice(-8)})` : 'Чат не инициализирован'}
            </span>
            <span>
              Нажмите Enter для отправки, Shift+Enter для новой строки
            </span>
          </div>
        </div>
      </div>
    </>
  )
}
