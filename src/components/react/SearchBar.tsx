import { useState } from 'react'
import { Input, But<PERSON>, Tabs, Tab } from '@heroui/react'
import { SearchIcon, XIcon } from 'lucide-react'
import { navigate } from 'astro:transitions/client'
import { clearStoreState } from '@/stores/qs'
import { InstantSearchAutocomplete } from '@/components/react/InstantSearchAutocomplete'

interface Props {
  tabPlacement: 'bottom' | 'top'
  isMobile: boolean
  size?: 'sm' | 'md' | 'lg'
  autofocus?: boolean
  isMounted?: boolean
  onSearch?: () => void
}

export default function SearchBar({ tabPlacement = 'bottom', isMobile = false, size, autofocus = false, onSearch }: Props) {
  const [searchMode, setSearchMode] = useState<'name' | 'size'>('name')

  // Обработчик изменения режима поиска
  const handleSearchModeChange = (key: string | number) => {
    setSearchMode(key as 'name' | 'size')
  }

  const [dIn, setDIn] = useState('')
  const [dOut, setDOut] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  // Обработчик поиска по размеру
  const handleSizeSearch = async () => {
    if (dIn.length < 1) {
      return false
    }
    setIsLoading(true)

    clearStoreState()

    try {
      await navigate(`/search/${encodeURIComponent(`${dIn}*${dOut}`)}`)
      // Вызываем колбэк onSearch, если он предоставлен
      if (onSearch) {
        onSearch()
      }
    } catch (error) {
      console.error('Ошибка при навигации:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div>
      {searchMode === 'name' ? (
        <InstantSearchAutocomplete autofocus={autofocus} isMobile={isMobile} searchMode={searchMode} size={size} onSearch={onSearch} />
      ) : (
        <div className='flex items-center space-x-2 rounded-lg bg-default-100 px-1'>
          <Input
            onKeyDown={(e) => e.key === 'Enter' && handleSizeSearch()}
            type='number'
            placeholder='D вн.'
            min={0}
            max={500}
            size={size}
            value={dIn}
            onValueChange={setDIn}
            aria-label='Внутренний диаметр'
          />
          <XIcon className='w-4 shrink-0 grow-0 font-bold' />
          <Input
            onKeyDown={(e) => e.key === 'Enter' && handleSizeSearch()}
            type='number'
            size={size}
            min={0}
            max={500}
            placeholder='D нар.'
            value={dOut}
            onValueChange={setDOut}
            aria-label='Наружный диаметр'
          />
          <Button isLoading={isLoading} size={size} isIconOnly onPress={handleSizeSearch} aria-label='Найти по размеру'>
            <SearchIcon size={18} aria-hidden='true' />
          </Button>
        </div>
      )}

      <div className='mt-1 flex justify-center'>
        <Tabs
          size={isMobile ? 'sm' : 'lg'}
          classNames={{
            tabList: 'md:dark bg-default-400 sm:bg-default',
            tabContent: 'md:dark font-semibold text-xs sm:text-sm',
            // tabWrapper: 'border',
            panel: 'hidden',
            cursor: 'bg-default-600 sm:bg-default-400 dark:bg-default-400 dark:sm:bg-default-400',
            base: 'flex justify-center'
          }}
          variant='solid'
          color='default'
          placement={tabPlacement}
          selectedKey={searchMode}
          onSelectionChange={handleSearchModeChange}
          aria-label='Режимы поиска'
        >
          <Tab key='name' title={<span className='text-white'>По названию</span>} />
          <Tab key='size' title={<span className='text-white'>По размеру</span>} />
        </Tabs>
      </div>
    </div>
  )
}
