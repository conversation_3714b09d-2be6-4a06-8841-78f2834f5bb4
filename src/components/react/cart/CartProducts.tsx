import { pluralizeProducts } from '@/lib/pluralizeProducts'
import { ProductList } from '../ProductList'
import { TrpcReactWrapper } from '../TrcpReactWrapper'
import { trpcReact } from '@/trpc'
import { useEffect, useState } from 'react'
import { cartIsLoading, cartItems, setCartIsLoading } from '@/stores/cart'
import { Paginator } from '../Paginator'
import { Skeleton, Alert, Button } from '@heroui/react'
import { useStore } from '@tanstack/react-store'
import UploadCart from './UploadCart'
import { UploadIcon } from 'lucide-react'

interface Props {
  initialData?: unknown
  limit?: number
  page?: number
  cookieHeader?: string
  isMobile?: boolean
  viewMode?: string
  cartErrors?: any[]
  stockWarns?: any[]
}

export const CartProducts = (props: Props) => {
  return (
    <>
      <TrpcReactWrapper>
        <Data {...props} />
      </TrpcReactWrapper>
    </>
  )
}

const Data = ({ limit = 200, page = 1, isMobile, viewMode = 'table' }: Props) => {
  // const $cartIsLoading = useStore(cartIsLoading)

  const { data, isLoading, isFetching, isError, isSuccess, refetch } = trpcReact.products.getCartProducts.useQuery(
    { page },
    {
      refetchOnMount: true,
      refetchOnReconnect: true,
      refetchOnWindowFocus: true,
      retryOnMount: true
    }
  )

  // Синхронизируем состояние загрузки корзины с состоянием загрузки tRPC запроса
  // useEffect(() => {
  //   // Обновляем глобальный стор cartIsLoading в зависимости от состояния загрузки запроса
  //   setCartIsLoading(isLoading || isFetching)
  // }, [isLoading, isFetching])

  useEffect(() => {
    const unSubscribe = cartItems.subscribe((v) => {
      // console.log('🛒 !CartProducts cartItems.subscribe triggered, refetching cart sum')
      refetch()
    })

    return () => {
      unSubscribe()
    }
  }, [])

  // Подсчет общего количества товаров до текущей категории
  let totalProductsBefore = 0
  const [showUpload, setShowUpload] = useState(false)

  return (
    <>
      <div className='justify-left mb-2 flex flex-wrap items-end gap-5'>
        <h2 className='text-2xl'>Корзина</h2>
        <div className='text-sm text-default-600'>
          <span>{data?.meta?.totalCount || 0}</span>
          <span className='ml-1'>{pluralizeProducts(data?.meta?.totalCount || 0)}</span>
          <div className='text-sm text-default-600'>
            Страница {page} из {data?.meta?.pageCount}
          </div>
        </div>
        <Button size={isMobile ? 'sm' : 'md'} variant='flat' onPress={() => setShowUpload((v) => !v)}>
          <UploadIcon className='w-5'/>
          <span className='text-xs sm:text-sm mr-2 underline font-semibold'>Загрузить из файла</span>
        </Button>
      </div>
      {showUpload && (
        <UploadCart
          onSuccess={() => {
            setShowUpload(false)
            // refetch()
            window.location.reload()
          }}
        />
      )}
      {isLoading ? (
        <div className='space-y-3'>
          {[...Array(5)].map((_, i) => (
            <Skeleton key={i} className='h-10 w-full rounded-lg' />
          ))}
        </div>
      ) : (
        ''
      )}

      {data?.categories?.map((categoryData, categoryIndex) => {
        const startIndex = totalProductsBefore + 1
        totalProductsBefore += categoryData.products.length

        return (
          <div key={categoryIndex} className='mb-5 rounded-lg border p-2 dark:border-default-200'>
            {<h2 className='pb-2 text-base'>{categoryData.categoryTitle || ''}</h2>}
            <ProductList
              isMobile={isMobile}
              viewMode={viewMode}
              enableAddToCartAnimate={false}
              cartMode={true}
              startIndex={startIndex}
              cardImageSize={300}
              enableSorting={false}
              data={{
                columns: categoryData.columns,
                data: categoryData.products
              }}
            />
          </div>
        )
      })}

      <div className='my-3 flex justify-end'>
        <Paginator isMobile={isMobile} limit={limit} total={data?.meta?.pageCount} initPage={page} />
      </div>
    </>
  )
}
