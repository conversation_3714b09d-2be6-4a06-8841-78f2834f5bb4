---
import { ClientRouter } from 'astro:transitions'
import LoadingIndicator from 'astro-loading-indicator/component'
import '../styles/global.css'
import '../styles/override.css'

import { NavbarMain } from '@/components/react/NavbarMain'
import { BackToTopButton } from '@components/react/BackToTopButton'
import Footer from '@components/Footer.astro'
import { MobileBottomBar } from '@components/react/BottomNavbar'
import { ToastProvider } from '@heroui/react'
import { Image } from 'astro:assets'
// import LogoImage from '../assets/rt_whitelgo.webp'
import { ProductEditorModal } from '@/components/react/ProductEditorModal'
import AnalyticsTracker from '@/components/react/AnalyticsTracker'
import { CartLoader } from '@/components/react/CartLoader'
import InitScript from '@/components/react/InitScript'

interface Props {
  title: string
  description: string
  image?: string // Добавляем опциональный пропс для изображения
}

const {
  title,
  description = 'Каталог сальников',
  image // Получаем image из пропсов
} = Astro.props
const isDarkTheme = Astro.cookies.get('theme')?.value === 'dark'
const isMobile = Astro.locals.isMobile
const isAdmin = Astro.locals.isAdmin
const isIOS = Astro.locals.isIOS

// Получаем ctoken для передачи в клиентский код
const ctoken = Astro.cookies.get('ctoken')?.value

// Analytics IDs
const gaMeasurementId = import.meta.env.PUBLIC_GA_MEASUREMENT_ID || '265614037'
const ymCounterId = import.meta.env.PUBLIC_YM_COUNTER_ID || '34401280'
---

<!doctype html>
<html lang='ru' transition:animate='fade'>
  <head>
    <meta charset='UTF-8' />
    <meta name='description' content={description} />
    <meta name='viewport' content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no' />
    <link rel='icon' href='/favicon.ico' sizes='any' />
    <link rel='icon' href='/favicon-32x32.png' type='image/png' sizes='32x32' />
    <link rel='icon' href='/favicon-16x16.png' type='image/png' sizes='16x16' />
    <link rel='icon' type='image/png' href='/favicon-96x96.png' sizes='96x96' />
    <!-- <link rel='icon' type='image/svg+xml' href='/favicon.svg' /> -->
    <link rel='apple-touch-icon' sizes='180x180' href='/apple-touch-icon.png' />
    <link rel='manifest' href='/site.webmanifest' />
    <meta name='generator' content={Astro.generator} />

    <!-- OpenGraph tags -->
    <meta property='og:title' content={title} />
    <meta property='og:description' content={description} />
    {image && <meta property='og:image' content={image} />}
    <meta property='og:type' content='website' />
    <meta property='og:site_name' content='Мир Сальников' />

    <!-- Twitter Card tags -->
    <meta name='twitter:card' content='summary_large_image' />
    <meta name='twitter:title' content={title} />
    <meta name='twitter:description' content={description} />
    {image && <meta name='twitter:image' content={image} />}

    <!-- <link rel='preconnect' href='https://fonts.googleapis.com' /> -->
    <!-- <link rel='preconnect' href='https://fonts.gstatic.com' crossorigin /> -->
    <!-- <link href='https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap' rel='stylesheet' /> -->

    <ClientRouter />
    <LoadingIndicator threshold={100} color='white' class='cursor-wait' />
    <title>{title}</title>

    <!-- Preload critical resources -->
    <link rel="preload" as="image" href="/rt_whitelgo.webp" fetchpriority="high" />
    <!-- <link rel="preconnect" href="https://mirsalnikov.ru" /> -->
    <!-- <link rel="dns-prefetch" href="https://mirsalnikov.ru" /> -->

    <!-- Настройки предзагрузки -->
    <meta name='astro:prefetch' content='false' />

    <script>
      document.addEventListener('astro:before-preparation', () => {
        // isLoading.set(true)
        // toast.loading('Загрузка...', {position: 'top-center', duration: 5000})
        document.documentElement.setAttribute('data-loading', 'true')
      })

      document.addEventListener('astro:page-load', () => {
        // isLoading.set(false)
        // toast.dismiss()
        document.documentElement.setAttribute('data-loading', 'false')
      })
    </script>
    <script is:inline define:vars={{ isDarkTheme }}>
      function getThemePreference() {
        if (typeof localStorage !== 'undefined' && localStorage.getItem('theme')) {
          return localStorage.getItem('theme')
        }
        // return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
        return 'light'
      }

      function setDarkMode(document) {
        const isDark = getThemePreference() === 'dark'

        // document.documentElement.dataset.theme = theme
        document.documentElement.classList[isDark ? 'add' : 'remove']('dark')
      }

      setDarkMode(document)

      document.addEventListener('astro:before-swap', (ev) => {
        // Передайте входящий документ, чтобы установить для него тему
        //console.log('@astro before swap')

        setDarkMode(ev.newDocument)
      })
    </script>

    <!-- Google Analytics -->
    <script async src=`https://www.googletagmanager.com/gtag/js?id=${gaMeasurementId}`></script>
  </head>
  <body class='min-h-screen pb-10 sm:pb-0'>
    <InitScript client:load />
    <CartLoader client:load />
    <AnalyticsTracker client:idle />
    <ToastProvider
      placement={isMobile ? 'top-right' : 'top-right'}
      transition:persist='toaster'
      toastProps={{ classNames: { motionDiv: 'z-50' } }}
      client:only='react'
    />
    <nav>
      <NavbarMain isMobile={isMobile} client:load transition:persist='navbar'>
        <Image transition:persist='logoImage' slot='logoComponent' height={70} width={320} class='w-48 sm:w-80' src="/rt_whitelgo.webp" alt='Logo' fetchpriority='high' />
      </NavbarMain>
    </nav>
    <main class='mt-3 p-1'>
      <slot />
      <BackToTopButton transition:persist client:only='react' />
    </main>
    <Footer as='footer' transition:persist='footer'>
      <div class='bg-zinc-900 p-3 text-white transition-all duration-200 ease-in-out dark:bg-zinc-800 md:p-5' slot='fallback'></div>
    </Footer>
    <div class='fixed bottom-0 left-0 right-0 z-50 flex w-full justify-center sm:hidden'>
      {isMobile && <MobileBottomBar isIOS={isIOS} client:only='react' />}
    </div>

    {isAdmin && <ProductEditorModal client:only='react' />}
  </body>
</html>

<style is:global>
  html[data-loading='true'] * {
    cursor: progress !important;
  }
</style>

<script>
  // добавьте этот скрипт
  import { isMobile } from '@/stores/isMobile'

  const mediaQuery = window.matchMedia('(max-width: 960px)') // или любой другой брейкпоинт
  const updateIsMobile = (event) => {
    //console.log('@@match media: ', event)

    // if (isMobile.get() !== event.matches) {
    //   window.location.reload()
    // }
    isMobile.set(event.matches)
  }
  mediaQuery.addEventListener('change', updateIsMobile)
  updateIsMobile(mediaQuery) // установите начальное значение на клиенте
</script>

<style is:global>
  html {
    /* font-family: Montserrat, system-ui, sans-serif; */
  }

  /* Глобальное отключение обводки при фокусе */
  *:focus {
    outline: none !important;
    box-shadow: none !important;
  }

  /* Отключение обводки для нативных элементов */
  input:focus,
  textarea:focus,
  select:focus,
  button:focus,
  a:focus,
  [role='button']:focus,
  [tabindex]:focus {
    outline: none !important;
    box-shadow: none !important;
  }

  /* Предотвращение зума на iOS при фокусе на инпутах */
  input[type='text'],
  input[type='number'],
  input[type='email'],
  input[type='tel'],
  input[type='search'],
  input[type='password'],
  textarea,
  select {
    font-size: 16px !important; /* iOS не зумит при размере шрифта 16px и больше */
    -webkit-appearance: none; /* Убирает стандартные стили iOS */
    -moz-appearance: none;
    appearance: none;
  }

  /* Исправления для iOS */
  @supports (-webkit-touch-callout: none) {
    /* Фиксация нижней панели навигации на iOS */
    .fixed {
      -webkit-transform: translateZ(0);
      transform: translateZ(0);
    }

    /* Предотвращение скрытия элементов под клавиатурой */
    body,
    html {
      height: -webkit-fill-available;
      position: relative;
    }
  }
</style>

<!-- Скрипт для инициализации глобальных переменных -->
<script is:inline define:vars={{ ctoken }}>
  // Инициализируем глобальные переменные
  window.__APP_CONFIG = window.__APP_CONFIG || {}
  window.__IS_ADMIN = false // По умолчанию пользователь не администратор

  if (ctoken) {
    // Сохраняем токен в конфигурации, не в DOM
    window.__APP_CONFIG.ctoken = ctoken || null
  }
</script>

<!-- Скрипт для установки статуса администратора -->
<script is:inline define:vars={{ isAdmin }}>
  // Устанавливаем статус администратора в клиентском хранилище
  if (isAdmin) {
    // Устанавливаем глобальную переменную для доступа в клиентском коде
    window.__IS_ADMIN = true
  }
</script>

<script defer is:inline define:vars={{ gaMeasurementId }}>
  // Google Analytics
  if (typeof window !== 'undefined') {
    window.dataLayer = window.dataLayer || []
    function gtag() {
      dataLayer.push(arguments)
    }
    window.gtag = gtag

    gtag('js', new Date())
    gtag('config', gaMeasurementId, { 'send_page_view': true })

    // Отслеживание переходов между страницами при использовании Astro View Transitions
    document.addEventListener('astro:page-load', () => {
      gtag('config', gaMeasurementId, {
        page_path: window.location.pathname
      })
    })
  }
</script>

<!-- Определение типов для TypeScript -->
<script>
  interface Window {
    dataLayer: any[]
    gtag: (...args: any[]) => void
  }
</script>
<!-- Yandex.Metrika counter -->
<script defer is:inline define:vars={{ ymCounterId }}>
  ;(function (m, e, t, r, i, k, a) {
    m[i] =
      m[i] ||
      function () {
        ;(m[i].a = m[i].a || []).push(arguments)
      }
    m[i].l = 1 * new Date()
    ;(k = e.createElement(t)), (a = e.getElementsByTagName(t)[0]), (k.async = 1), (k.src = r), a.parentNode.insertBefore(k, a)
  })(window, document, 'script', 'https://mc.yandex.ru/metrika/tag.js', 'ym')

  ym(ymCounterId, 'init', {
    clickmap: false,
    trackLinks: true,
    accurateTrackBounce: true,
    webvisor: false
  })

  // Отслеживание переходов между страницами при использовании Astro View Transitions
  document.addEventListener('astro:page-load', () => {
    ym(ymCounterId, 'hit', window.location.pathname)
  })
</script>
<noscript><div><img src={`https://mc.yandex.ru/watch/${ymCounterId}`} style='position:absolute; left:-9999px;' alt='' /></div></noscript>
<!-- /Yandex.Metrika counter -->
