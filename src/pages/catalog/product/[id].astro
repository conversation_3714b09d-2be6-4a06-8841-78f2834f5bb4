---
import { priceFormat } from '@/lib/priceFormat'
import { trpc } from '@/trpc'
import ProductBreadcrumb from '@components/ProductBreadcrumb.astro'
import ProductSimilar from '@components/ProductSimilar.astro'
import { ImageViewer } from '@components/react/ImageViewer'
import { QtyInputWithStoreObserver } from '@components/react/QtyInputWithStoreObserver'
import { SchemaViewer } from '@components/react/SchemaViewer'
import { ProductEditButton } from '@components/react/ProductEditButton'
import { getBaseUrl, getRtiImageUrl, getImageUrl } from '@/lib/config'

import Layout from '@layouts/Layout.astro'
import { Skeleton } from '@heroui/react'
import { ChevronRightIcon } from 'lucide-react'
import { ProductRkItems } from '@components/react/ProductRkItems'
import { Image } from 'astro:assets'
import { QtyInputSkeleton } from '@components/react/QtyInputSkeleton'
import ProductSchema from '@components/ProductSchema.astro'
import { ProductSeoDescription } from '@components/react/ProductSeoDescription'

const id = Number(Astro.params['id'])

if (!id) {
  return Astro.redirect('/catalog/product/404')
}

// Условное кеширование
const isMobile = Astro.locals.isMobile
const isAdmin = Astro.locals.isAdmin
const cacheTime = isMobile ? 1800 : 3600 // Разное время кеширования для мобильных и десктопных версий

Astro.response.headers.set('Cache-Control', `public, max-age=${cacheTime}, stale-while-revalidate=${cacheTime}`)
Astro.response.headers.set('Vary', 'Accept, Cookie, Accept-Encoding, Accept-Language, User-Agent')

// Добавляем Last-Modified header
// Astro.response.headers.set('Last-Modified', new Date().toUTCString())

// Кэширование запроса на 1 час
let data: Awaited<ReturnType<typeof trpc.products.getProductByIdMeili.query>> | null = null
try {
  data = await trpc.products.getProductByIdMeili.query(id, {
    context: {
      headers: {
        'Cache-Control': 'public, max-age=1800'
      }
    }
  })

  if (!data) {
    return Astro.redirect('/catalog/product/notfound')
  }
} catch (error) {
  console.log('🚀 ~ error:', error)
  return Astro.redirect('/catalog/product/notfound')
}

if (!data?.product?.prod_id) {
  return Astro.redirect('/catalog/product/notfound')
}

const { product, fields } = data

// Подготовка SEO-данных
const productName = `${product.prod_purpose} ${product.prod_sku} / ${product.prod_analogsku} ${product.prod_size}`
const productFullName = `${product.prod_purpose} ${product.prod_sku} / ${product.prod_analogsku}, Бренд: ${product.prod_manuf} Размер: ${product.prod_size}, ${product.prod_analogs}`
const productDescription = `Купить ${productFullName}. Цена: ${priceFormat(product.prod_price)}. Доставка по России.`

// Получаем базовый URL
const baseUrl = getBaseUrl(Astro.url)

// Формируем URL изображений
const productImages = [
  getRtiImageUrl(product?.prod_img + '.jpg' || product?.prod_analogsku + '.jpg'),
  ...(Array.isArray(product.images) ? product.images.map((i) => getImageUrl(i.path)) : []),
  getRtiImageUrl(product?.prod_type + '.jpg')
]

// Формируем URL главного изображения
const mainImage = getRtiImageUrl(product?.prod_img + '.jpg' || product?.prod_analogsku + '.jpg')

// Формируем абсолютный URL текущей страницы
const currentUrl = new URL(Astro.url.pathname, Astro.url.href).toString()

// Убедимся, что URL абсолютный для микроразметки
const absoluteUrl = currentUrl.startsWith('http') ? currentUrl : `${baseUrl}${Astro.url.pathname}`

// Расширенные структурированные данные
const structuredData = {
  '@context': 'https://schema.org/',
  '@type': 'Product',
  name: productName,
  description: productDescription,
  sku: product.prod_analogsku,
  brand: {
    '@type': 'Brand',
    name: product.prod_manuf
  },
  manufacturer: {
    '@type': 'Organization',
    name: product.prod_manuf
  },
  model: product.prod_sku,
  productID: product.prod_analogsku,
  category: product.prod_type,
  image: productImages.map((img) => ({
    '@type': 'ImageObject',
    'url': img,
    'width': '800',
    'height': '800'
  })),
  review: {
    '@type': 'Review',
    'reviewRating': {
      '@type': 'Rating',
      'ratingValue': '5',
      'bestRating': '5'
    },
    'author': {
      '@type': 'Organization',
      'name': 'Мир Сальников'
    }
  },
  aggregateRating: {
    '@type': 'AggregateRating',
    ratingValue: '5',
    bestRating: '5',
    worstRating: '1',
    ratingCount: '1'
  },
  offers: {
    '@type': 'Offer',
    url: absoluteUrl,
    price: product.prod_price,
    priceCurrency: 'RUB',
    priceValidUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 дней
    itemCondition: 'https://schema.org/NewCondition',
    availability: product.prod_count > 0 ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock',
    seller: {
      '@type': 'Organization',
      name: 'Мир Сальников'
    },
    wholesalePrice: product.whosaleprice || 0
  }
}

// Обновляем breadcrumbs с динамическим URL
const breadcrumbsItems = [
  {
    '@type': 'ListItem',
    'position': 1,
    'name': 'Главная',
    'item': baseUrl
  },
  {
    '@type': 'ListItem',
    'position': 2,
    'name': 'Каталог',
    'item': `${baseUrl}/catalog`
  },
  {
    '@type': 'ListItem',
    'position': 3,
    'name': product.prod_type,
    'item': `${baseUrl}/catalog/${product.prod_type}`
  },
  {
    '@type': 'ListItem',
    'position': 4,
    'name': productName,
    'item': currentUrl
  }
]
---

<Layout title={productFullName} description={productDescription} image={mainImage}>
  <!-- RDFa разметка для товара -->
  <div vocab='https://schema.org/' typeof='Product' style='display:none'>
    <span property='name'>{productFullName}</span>
    <span property='description'>{productDescription}</span>
    <span property='image'>{mainImage}</span>
    <span property='url'>{absoluteUrl}</span>
    <span property='sku'>{product.prod_analogsku}</span>
    <span property='brand' typeof='Brand'>
      <span property='name'>{product.prod_manuf}</span>
    </span>
    <span property='offers' typeof='Offer'>
      <span property='price'>{product.prod_price}</span>
      <span property='priceCurrency'>RUB</span>
      <span property='availability'>{product.prod_count > 0 ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock'}</span>
      <span property='wholesalePrice'>{product.whosaleprice || 0}</span>
    </span>
  </div>
  <!-- Основные мета-теги -->
  <link rel='canonical' href={currentUrl} />

  <!-- Open Graph -->
  <meta property='og:type' content='product' />
  <meta property='og:url' content={absoluteUrl} />
  <meta property='og:title' content={productFullName} />
  <meta property='og:description' content={productDescription} />
  <meta property='og:image' content={mainImage} />
  <meta property='og:site_name' content='Мир Сальников' />

  <!-- Для мессенджеров -->
  <meta property='og:image:width' content='800' />
  <meta property='og:image:height' content='800' />
  <meta property='og:image:alt' content={productFullName} />

  <!-- Только нужные Twitter теги -->
  <meta name='twitter:card' content='summary_large_image' />
  <meta name='twitter:image' content={mainImage} />
  <meta name='twitter:title' content={productFullName} />
  <meta name='twitter:description' content={productDescription} />

  <!-- Дополнительные теги для WhatsApp и Telegram -->
  <meta property='og:image:secure_url' content={mainImage} />
  <meta property='og:image:alt' content={productFullName} />

  <!-- Дополнительные мета-теги -->
  <meta name='keywords' content={`${product.prod_purpose}, ${product.prod_sku}, ${product.prod_manuf}, ${product.prod_type}, сальник, РТИ, купить сальник`} />
  <meta name='robots' content='index, follow' />
  <meta name='author' content='Мир Сальников' />

  <!-- Preload изображений -->
  <link rel='preload' href={mainImage} as='image' fetchpriority='high' />
  {product.images?.map((img) => <link rel='preload' href={getImageUrl(img.path)} as='image' fetchpriority='low' />)}

  <!-- Structured Data -->
  <script
    is:inline
    type='application/ld+json'
    set:html={JSON.stringify({
      ...structuredData,
      url: absoluteUrl
    })}
  />

  <!-- Дополнительная разметка для хлебных крошек -->
  <script
    is:inline
    type='application/ld+json'
    set:html={JSON.stringify({
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      'itemListElement': breadcrumbsItems
    })}
  />

  <!-- DNS Preconnect -->
  <link rel='preconnect' href={baseUrl} />
  <link rel='dns-prefetch' href={baseUrl} />

  <!-- Preload critical assets -->
  <link rel='preload' href={mainImage} as='image' fetchpriority='high' />

  <div class='mx-auto px-4 py-8 sm:px-6 lg:px-8'>
    <div>
      <ProductBreadcrumb server:defer transition:persist product={product}>
        <div slot='fallback'>
          <nav aria-label='Breadcrumb' class='mx-4'>
            <ol class='flex items-center'>
              {
                [1, 2, 3].map((item, index) => (
                  <li class='flex items-center'>
                    {index > 0 && (
                      <span class='mx-2 h-4 w-6 text-sm text-default-300' aria-hidden='true'>
                        <ChevronRightIcon className='h-4 w-4' />
                      </span>
                    )}
                    <Skeleton className='h-6 w-24 rounded' />
                  </li>
                ))
              }
            </ol>
          </nav>
        </div>
      </ProductBreadcrumb>
    </div>

    <div class='mt-8 grid grid-cols-1 gap-12 md:grid-cols-2'>
      <!-- Right Column - Product Info -->
      <div class='space-y-8'>
        <div class='border-b border-default-200 pb-6'>
          <h1 class='text-base font-bold text-default-800 sm:text-xl md:text-2xl'>
            {product.prod_purpose}
            {product.prod_sku}
            {product.prod_manuf}
            {product.prod_size}
          </h1>
          <p class='mt-2 text-default-800'>Аналог: {product.prod_analogsku}</p>
          {
            isAdmin && (
              <div>
                <ProductEditButton productId={id} client:visible />
              </div>
            )
          }
        </div>

        <div class='flex flex-wrap items-center justify-between'>
          <div class='space-y-2'>
            <p class='text-base font-bold text-default-800 sm:text-xl'>
              Цена: {priceFormat(product.prod_price)}
            </p>
            <p class='text-base font-semibold text-default-800 sm:text-xl'>
              Опт: {priceFormat(product.whosaleprice || 0)}
            </p>
            <div class='flex items-center gap-2'>
              <div class={`w-3 h-3 rounded-full ${product.prod_count > 0 ? 'bg-success-500' : 'bg-danger-500'}`}></div>
              <span class='text-sm text-default-600 md:text-base'>
                {product.prod_count > 0 ? 'В наличии' : 'Нет в наличии'}: {product.prod_count} шт
              </span>
            </div>
          </div>
          <div class='flex gap-2'>
            <div class='w-38 rounded-lg border p-3'>
              <QtyInputWithStoreObserver label='В корзину' product={product} client:only='react'>
                <div slot='fallback'>
                  <QtyInputSkeleton qty={product.qty} />
                </div>
              </QtyInputWithStoreObserver>
            </div>
          </div>
        </div>

        <div class='space-y-4 rounded-xl bg-default-50 p-6'>
          {
            fields
              .filter((field) => {
                // Проверяем наличие значения в поле
                const value = product[field.keyname]
                return value !== undefined && value !== null && value !== ''
              })
              .map((field) => (
                <div class='flex flex-col justify-between gap-2 border-b border-default-200 py-2 last:border-none sm:flex-row sm:items-center'>
                  <div class='text-base font-medium text-default-700'>{field.title}</div>
                  <div class='max-w-full text-right sm:max-w-80'>
                    {'type' in field && field.type === 'html' ? (
                      <div set:html={product[field.keyname]} class='text-right text-base text-default-900' />
                    ) : 'type' in field && field.type === 'array' ? (
                      <div class='flex flex-wrap justify-end gap-2'>
                        {product[field.keyname]
                          ?.split(',')
                          .map((i: string) => i.trim())
                          .filter(Boolean)
                          .map((item: any) => (
                            <span data-astro-prefetch='tap' class='rounded-full bg-default-100 px-2 py-1 text-sm transition-colors hover:bg-default-200'>
                              {item}
                            </span>
                          ))}
                      </div>
                    ) : (
                      <span class={`text-base ${'searchable' in field && field.searchable ? 'hover:underline' : 'text-default-900'}`}>
                        {'searchable' in field && field.searchable ? (
                          <a data-astro-prefetch='tap' href={`/search/${encodeURIComponent(product[field.keyname])}`}>
                            {product[field.keyname]}
                          </a>
                        ) : (
                          product[field.keyname]
                        )}
                      </span>
                    )}
                  </div>
                </div>
              ))
          }
        </div>
        {
          product.prod_rk && (
            <div>
              <h3 class='mb-3 font-semibold text-default-800 md:text-lg'>Состав ремкомлекта </h3>
              <ProductRkItems rkString={product.prod_rk} skeletonCount={product.prod_rk?.split?.(',')?.length} id={product.prod_id} client:only='react' />
            </div>
          )
        }
      </div>

      <!-- Left Column - Images -->
      <div class='sticky top-8'>
        <div class='flex flex-col gap-5'>
          <div class='rounded-lg border border-default-300 p-3'>
            <ImageViewer imageHeight={450} client:visible product={product}>
              <div class='rounded-lg border p-3' slot='fallback'>
                <Image
                  src={getRtiImageUrl(`${product?.prod_img || product?.prod_analogsku}.jpg`)}
                  width={600}
                  height={450}
                  alt={`${product.prod_purpose} ${product.prod_sku} ${product.prod_size}`}
                />
              </div>
            </ImageViewer>
          </div>
          <!-- {
            schema && (
              <div class='flex flex-col gap-2 rounded-lg border p-3'>
                <div>
                  <SchemaViewer isMobile={isMobile} server:defer client:only='react' data={schema} product={product} />
                </div>
              </div>
            )
          } -->

          <div class='flex flex-col gap-2 rounded-lg border p-3'>
            <div>
              <ProductSchema server:defer product={product} />
            </div>
          </div>
        </div>
      </div>
    </div>
    <ProductSeoDescription product={product} client:visible />
  </div>
</Layout>
