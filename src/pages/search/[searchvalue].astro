---
import { ProductList } from '@/components/react/ProductList'
import Layout from '@/layouts/Layout.astro'
import { trpc } from '@/trpc'
import { updateURLParams } from '@/stores/qs'
import ProductFilters from '@components/ProductFilters.astro'
import { pluralizeProducts } from '@/lib/pluralizeProducts'
import { getInitialFilterValue, getPageParams } from '@/lib/pageUtils'
// import FilterSkeleton from '@components/FilterSkeleton.astro'
import { BackHistoryButton } from '@components/react/BackHistoryButton'
import ProductViewSwitcher from '@components/ProductViewSwitcher.astro'
import { ChevronRightIcon } from 'lucide-react'
import ProductPaginator from '@components/ProductPaginator.astro'
import { CSorting } from '@/components/react/CSorting'

// Устанавливаем кэширование страницы на 2 минуты
Astro.response.headers.set('Cache-Control', 'public, max-age=120, s-maxage=120')
Astro.response.headers.set('Vary', 'Accept, Cookie, Accept-Encoding, Accept-Language, User-Agent')

const searchParams = Astro.url.searchParams
const searchvalue = Astro.params.searchvalue
const isMobile = Astro.locals.isMobile
const isAdmin = Astro.locals.isAdmin

const { limit, page, order, sorting, filters } = getPageParams(searchParams)
const { filtersValue, filtersStr } = getInitialFilterValue(searchParams)

const response = await trpc.products.globalSearchMeili.query({
  limit: Math.min(Number(limit), 50),
  page,
  order,
  sorting,
  filters,
  searchvalue
})

const productTotalCount = response?.meta?.totalCount || 0
const categoriesIds = response?.categories?.map((category) => category.categoryId)

updateURLParams(
  {
    filters,
    page: page ? Number(page) : null,
    sorting
  },
  false
)

const currentViewMode = Astro.locals.viewMode

// Формируем SEO-данные
const currentUrl = new URL(Astro.url.pathname, Astro.url.href).toString()
const canonicalUrl = new URL(Astro.url.pathname.split('/page/')[0], Astro.url.href).toString()
const decodedSearchValue = decodeURIComponent(response?.VAL || searchvalue)

// Получаем базовый URL сайта
const baseUrl = `${Astro.url.protocol}//${Astro.url.host}`

// Получаем URL для пагинации
const getPageUrl = (pageNum: number) => {
  const url = new URL(Astro.url)
  url.searchParams.set('page', pageNum.toString())
  return url.toString()
}

const searchTitle = `${decodedSearchValue} - поиск по каталогу РТИ. Найдено ${productTotalCount} ${pluralizeProducts(productTotalCount)}`
const searchDescription = `Результаты поиска по запросу: ${decodedSearchValue}. ${productTotalCount > 0 ? `Найдено ${productTotalCount} ${pluralizeProducts(productTotalCount)}. ` : 'По вашему запросу ничего не найдено. '}Доставка по России.`

// Создаем массив с breadcrumbs для микроразметки
const breadcrumbsItems = [
  {
    '@type': 'ListItem',
    'position': 1,
    'name': 'Главная',
    'item': baseUrl
  },
  {
    '@type': 'ListItem',
    'position': 2,
    'name': 'Поиск',
    'item': `${baseUrl}/search`
  },
  {
    '@type': 'ListItem',
    'position': 3,
    'name': `Поиск: ${decodedSearchValue}`,
    'item': canonicalUrl
  }
]

// Модифицируем Schema.org разметку для использования динамического URL
const schemaData = {
  '@context': 'https://schema.org',
  '@type': 'SearchResultsPage',
  'name': searchTitle,
  'description': searchDescription,
  'url': currentUrl,
  'breadcrumb': {
    '@type': 'BreadcrumbList',
    'itemListElement': breadcrumbsItems
  },
  'mainEntity': {
    '@type': 'ItemList',
    'numberOfItems': productTotalCount,
    'itemListElement': response.categories.flatMap((category, categoryIndex) =>
      category.products.map((product, index) => ({
        '@type': 'ListItem',
        'position': categoryIndex * 1000 + index + 1,
        'item': {
          '@type': 'Product',
          'name': `${product.prod_purpose} ${product.prod_sku}`,
          'url': `${baseUrl}/catalog/product/${product.prod_id}`,
          'offers': {
            '@type': 'Offer',
            'price': product.prod_price,
            'priceCurrency': 'RUB',
            'availability': product.prod_count > 0 ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock'
          }
        }
      }))
    )
  }
}
---

<Layout title={searchTitle} description={searchDescription}>
  <!-- Основные мета-теги -->
  <link rel='canonical' href={canonicalUrl} />

  <!-- Пагинация для SEO -->
  {page > 1 && <link rel='prev' href={getPageUrl(page - 1)} />}
  {page < response?.meta?.pageCount && <link rel='next' href={getPageUrl(page + 1)} />}

  <!-- Open Graph -->
  <meta property='og:type' content='website' />
  <meta property='og:title' content={searchTitle} />
  <meta property='og:description' content={searchDescription} />
  <meta property='og:url' content={currentUrl} />
  <meta property='og:site_name' content='Мир Сальников' />

  <!-- Мета-теги для поисковых систем -->
  <meta name='robots' content='noindex, follow' />
  <meta name='keywords' content={`${decodedSearchValue}, РТИ, купить, цена, ${response.categories.map((cat) => cat.categoryTitle).join(', ')}`} />

  <!-- Schema.org разметка -->
  <script type='application/ld+json' set:html={JSON.stringify(schemaData)} />

  <div class='flex w-full flex-wrap xl:gap-5'>
    <!-- {
      !isMobile && (
        <div class='relative hidden w-1/4 max-w-48 md:min-w-36 xl:md:min-w-40 xl:block'>
          <ProductFilters server:defer initialFilterValue={filtersValue} categoryId={categoriesIds} search={searchvalue} isMobile={false} isSticky={false}>
            <div slot='fallback' class='relative -mt-4 min-w-36 max-w-48 space-y-3 p-2 xl:min-w-40'>
              {response?.categories?.map((category) => category.filters?.filter((i) => i.title).map((i) => <FilterSkeleton title={i.title || ''} />))}
            </div>
          </ProductFilters>
        </div>
      )
    } -->

    <div class='relative w-full shrink grow xl:container xl:mx-auto xl:w-3/4'>
      <div class='flex flex-wrap items-end justify-between'>
        <div>
          {
            productTotalCount ? (
              <div>
                <BackHistoryButton variant='light' size='sm' label='Назад' client:idle />
              </div>
            ) : (
              ''
            )
          }
          <div class='justify-left mt-2 flex items-center gap-5'>
            <div class='rounded-lg border-2 p-2 text-sm sm:text-base md:text-xl'>
              Поиск: <span class='font-semibold'>{decodedSearchValue}</span>
            </div>
            <div>
              {filtersStr && <div class='text-sm text-default-600'>({filtersStr})</div>}

              <div class='mt-1 flex items-center text-sm text-default-600'>
                {productTotalCount >= 1000 && <ChevronRightIcon className='w-4' />}
                {productTotalCount}
                {pluralizeProducts(productTotalCount)}
              </div>
              <div class='text-sm text-default-600'>
                Страница {page} из {response?.meta?.pageCount}
              </div>
            </div>
          </div>
        </div>

        {
          !isMobile && currentViewMode == 'grid' && (
            <div class='hidden sm:block'>
              <CSorting
                client:only='react'
                variant='bordered'
                size='md'
                label='Сортировать по'
                columns={response?.categories?.map((i) => i.columns).flat() || []}
                server:defer
                initialSorting={sorting}
                isMobile={isMobile}
              >
                <div slot='fallback' class='h-10 w-24 animate-pulse rounded-lg bg-default-100 ease-in-out' />
              </CSorting>
            </div>
          )
        }

        {
          !isMobile && (
            <div class='flex justify-end'>
              <ProductViewSwitcher server:defer />
            </div>
          )
        }
      </div>

      <div class='mt-3 w-full sm:hidden'>
        {
          isMobile && (
            <div class='flex w-full justify-between gap-2 xl:hidden'>
              <CSorting
                client:only='react'
                variant='bordered'
                size='sm'
                columns={response?.categories?.map((i) => i.columns).flat() || []}
                initialSorting={sorting}
                isMobile={true}
              >
                <div slot='fallback' class='h-10 w-24 animate-pulse rounded-lg bg-default-100 ease-in-out' />
              </CSorting>
              <ProductFilters initialFilterValue={filtersValue} categoryId={categoriesIds} search={decodedSearchValue} transition:persist isMobile={true}>
                <div slot='fallback' class='flex h-14 justify-end gap-2'>
                  {response?.categories?.map((category) =>
                    category.filters
                      ?.filter((i) => i.title)
                      .map((i) => (
                        <div class='flex h-10 w-24 animate-pulse items-center justify-center rounded-md bg-default-200 text-center text-sm'>
                          <div class=''>{i.title || ''}</div>
                        </div>
                      ))
                  )}
                </div>
              </ProductFilters>
            </div>
          )
        }
      </div>

      <div class='mt-3 gap-5'>
        {
          !productTotalCount && (
            <div class='flex h-20 flex-col items-center justify-center gap-5'>
              <BackHistoryButton label='Вернуться назад' client:idle />
              <div>Нет результатов по вашему запросу</div>
            </div>
          )
        }

        {
          response.categories.map((categoryData, index) => (
            <div class='mb-5 rounded-lg border p-2 dark:border-default-200'>
              <h2 class='pb-2 text-base'>
                Найдено в: <span class='font-semibold'>{String(categoryData.categoryTitle || '')}</span>
              </h2>
              <ProductList
                showCounter={index + 1}
                isMobile={isMobile}
                viewMode={isMobile ? 'grid' : currentViewMode}
                initialSorting={sorting}
                data={{
                  columns: categoryData.columns,
                  data: categoryData.products
                }}
                searchvalue={decodedSearchValue}
                editorModeEnable={isAdmin}
                client:load
              />
            </div>
          ))
        }
      </div>
      <!--080425 removed  -->
      <ProductPaginator isMobile={isMobile} transition:persist limit={limit} total={response?.meta?.pageCount} initPage={page} />
    </div>
  </div>
</Layout>
